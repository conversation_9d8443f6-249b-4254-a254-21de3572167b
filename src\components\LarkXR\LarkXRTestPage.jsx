// src/components/LarkXR/LarkXRTestPage.jsx
import React, { useState } from 'react';
import LarkXRControlOptions from './LarkXRControlOptions';

const LarkXRTestPage = () => {
  const [showDropdown, setShowDropdown] = useState(false);
  const [showBottomSheet, setShowBottomSheet] = useState(false);

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">LarkXR Controls Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Dropdown Test */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Dropdown Controls Test</h2>
            <div className="relative">
              <button
                onClick={() => setShowDropdown(!showDropdown)}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
              >
                Toggle Dropdown
              </button>
              
              {showDropdown && (
                <div className="absolute top-10 left-0 bg-white rounded-lg shadow-lg p-2 w-48 z-50 border">
                  <LarkXRControlOptions
                    larksr={null}
                    isConnected={true}
                    showInDropdown={true}
                    onClose={() => setShowDropdown(false)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Bottom Sheet Test */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Bottom Sheet Controls Test</h2>
            <button
              onClick={() => setShowBottomSheet(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Open Bottom Sheet
            </button>
          </div>
        </div>

        {/* Test Instructions */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Test Instructions</h3>
          <ul className="text-blue-800 space-y-1">
            <li>• Click "Toggle Dropdown" to test dropdown controls</li>
            <li>• Click "Open Bottom Sheet" to test mobile-style controls</li>
            <li>• Try clicking each control option (Restart, Network, Settings, Help)</li>
            <li>• Check browser console for debug messages</li>
            <li>• Verify modals open and close properly</li>
          </ul>
        </div>

        {/* Bottom Sheet */}
        {showBottomSheet && (
          <>
            <div 
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setShowBottomSheet(false)}
            />
            
            <div className="fixed bottom-0 left-0 right-0 z-50 bg-white rounded-t-2xl max-h-[80vh] overflow-y-auto">
              <div className="flex justify-center py-3">
                <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
              </div>

              <div className="px-6 pb-4 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">LarkXR Controls Test</h2>
                <p className="text-sm text-gray-600">Testing bottom sheet layout</p>
              </div>

              <div className="p-6">
                <LarkXRControlOptions
                  larksr={null}
                  isConnected={true}
                  showInDropdown={false}
                  onClose={() => setShowBottomSheet(false)}
                />
              </div>

              <div className="p-6 border-t border-gray-200">
                <button
                  onClick={() => setShowBottomSheet(false)}
                  className="w-full py-3 px-4 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default LarkXRTestPage;
