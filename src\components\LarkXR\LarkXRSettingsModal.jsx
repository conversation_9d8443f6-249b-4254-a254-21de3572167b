// src/components/LarkXR/LarkXRSettingsModal.jsx
import React, { useState, useEffect } from 'react';
import BottomSheet from '../common/BottomSheet';
import { getScreenSize } from '../../utils/screenUtils';

const LarkXRSettingsModal = ({ larksr, isConnected, onClose }) => {
  const [activeTab, setActiveTab] = useState('Settings'); // 'Settings' or 'Frame'
  const [screenSize, setScreenSize] = useState(getScreenSize());
  const [settings, setSettings] = useState({
    // Settings tab
    qualityMode: 'HD', // 'Auto', 'SD', 'HD', 'UHD'
    ratioMode: 'Auto', // 'Auto', 'Zoom', 'HD', 'UHD'
    viewMode: 'Portrait', // 'Portrait', 'Landscape'
    resolutionMode: 'Mobile Small', // 'Mobile Small', 'Mobile Large', 'Tablet', 'Laptop', 'Desktop'

    // Frame tab
    customBitrate: 20000,
    customFps: 60,
    scaleMode: false, // false = Auto, true = Scale
    resolution: { id: '19201080', width: 1920, height: 1080 },
    partialMode: false
  });

  // Handle screen resize
  useEffect(() => {
    const handleResize = () => {
      setScreenSize(getScreenSize());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Check if mobile/tablet for bottom sheet
  const isMobileOrTablet = screenSize === 'mobile' || screenSize === 'tablet';

  // Settings options
  const qualityOptions = ['Auto', 'SD', 'HD', 'UHD'];
  const ratioOptions = ['Auto', 'Zoom', 'HD', 'UHD'];
  const viewOptions = ['Portrait', 'Landscape'];
  const resolutionOptions = ['Mobile Small', 'Mobile Large', 'Tablet', 'Laptop', 'Desktop'];

  // Frame tab options
  const coderates = [8000, 10000, 15000, 20000, 30000, 50000, 100000];
  const fpsList = [30, 60];
  const resolutions = [
    { id: '40961080', width: 4096, height: 1080 },
    { id: '40962160', width: 4096, height: 2160 },
    { id: '38402160', width: 3840, height: 2160 },
    { id: '38401080', width: 3840, height: 1080 },
    { id: '25601440', width: 2560, height: 1440 },
    { id: '20481536', width: 2048, height: 1536 },
    { id: '19201080', width: 1920, height: 1080 },
    { id: '19201440', width: 1920, height: 1440 },
    { id: '1600900', width: 1600, height: 900 },
    { id: '1366768', width: 1366, height: 768 },
    { id: '1280720', width: 1280, height: 720 },
    { id: '1280600', width: 1280, height: 600 }
  ];

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));

    // Apply setting immediately if connected
    if (isConnected && larksr) {
      try {
        switch (key) {
          case 'customBitrate':
            console.log('Applying bitrate:', value);
            if (larksr.setVideoBitrateKbps) {
              larksr.setVideoBitrateKbps(value);
            }
            break;
          case 'customFps':
            console.log('Applying FPS:', value);
            if (larksr.setVideoFps) {
              larksr.setVideoFps(value);
            }
            break;
          case 'resolution':
            console.log('Applying resolution:', value);
            if (larksr.setCloudAppSize) {
              larksr.setCloudAppSize(value.width, value.height);
            }
            break;
          default:
            console.log('Setting changed:', key, value);
        }
      } catch (error) {
        console.error('Failed to apply setting:', error);
      }
    }
  };

  const handleQualityClick = (qualityType) => {
    setSettings(prev => ({ ...prev, qualityMode: qualityType }));

    const qualitySettings = {
      'Auto': { bitrate: 8000, fps: 60 },
      'SD': { bitrate: 10000, fps: 60 },
      'HD': { bitrate: 20000, fps: 60 },
      'UHD': { bitrate: 50000, fps: 60 }
    };

    const setting = qualitySettings[qualityType];
    if (setting) {
      handleSettingChange('customBitrate', setting.bitrate);
      handleSettingChange('customFps', setting.fps);
    }
  };

  const toggleSwitch = (key) => {
    handleSettingChange(key, !settings[key]);
  };

  // Render button group for settings
  const renderButtonGroup = (options, currentValue, onChange) => (
    <div className="flex bg-gray-100 rounded-md">
      {options.map((option, index) => (
        <button
          key={option}
          onClick={() => onChange(option)}
          className={`px-3 py-1 text-xs font-medium transition-colors ${
            index === 0 ? 'rounded-l-md' : index === options.length - 1 ? 'rounded-r-md' : ''
          } ${
            currentValue === option ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
          }`}
        >
          {option}
        </button>
      ))}
    </div>
  );

  // Render content for both modal and bottom sheet
  const renderContent = () => (
    <>
      {/* Header with Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex items-center justify-between p-4">
          <div className="flex">
            <button
              onClick={() => handleTabClick('Settings')}
              className={`px-4 py-2 text-sm font-medium rounded-l-md transition-colors ${
                activeTab === 'Settings'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Settings
            </button>
            <button
              onClick={() => handleTabClick('Frame')}
              className={`px-4 py-2 text-sm font-medium rounded-r-md transition-colors ${
                activeTab === 'Frame'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Frame
            </button>
          </div>
          {!isMobileOrTablet && (
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="p-6 max-h-[70vh] overflow-y-auto">
        {/* Settings Tab */}
        {activeTab === 'Settings' && (
          <div className="space-y-4">
            {/* Quality */}
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <span className="text-sm font-medium text-gray-700">Quality</span>
              {renderButtonGroup(qualityOptions, settings.qualityMode, (value) => handleQualityClick(value))}
            </div>

            {/* Ratio */}
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <span className="text-sm font-medium text-gray-700">Ratio</span>
              {renderButtonGroup(ratioOptions, settings.ratioMode, (value) => handleSettingChange('ratioMode', value))}
            </div>

            {/* View */}
            <div className="flex items-center justify-between py-3 border-b border-gray-100">
              <span className="text-sm font-medium text-gray-700">View</span>
              {renderButtonGroup(viewOptions, settings.viewMode, (value) => handleSettingChange('viewMode', value))}
            </div>

            {/* Resolution */}
            <div className="flex items-center justify-between py-3">
              <span className="text-sm font-medium text-gray-700">Resolution</span>
              {renderButtonGroup(resolutionOptions, settings.resolutionMode, (value) => handleSettingChange('resolutionMode', value))}
            </div>
          </div>
        )}

        {/* Frame Tab */}
        {activeTab === 'Frame' && (
          <div className="space-y-4">
            {/* Custom Settings */}
            <div className="py-3 border-b border-gray-100">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Code rate</label>
                  <select
                    value={settings.customBitrate}
                    onChange={(e) => handleSettingChange('customBitrate', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {coderates.map((rate) => (
                      <option key={rate} value={rate}>{rate} kbps</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">FPS</label>
                  <select
                    value={settings.customFps}
                    onChange={(e) => handleSettingChange('customFps', parseInt(e.target.value))}
                    className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {fpsList.map((fps) => (
                      <option key={fps} value={fps}>{fps} FPS</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Resolution */}
            <div className="py-3 border-b border-gray-100">
              <label className="block text-sm font-medium text-gray-700 mb-2">Resolution</label>
              <select
                value={settings.resolution.id}
                onChange={(e) => {
                  const selectedRes = resolutions.find(r => r.id === e.target.value);
                  handleSettingChange('resolution', selectedRes);
                }}
                className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {resolutions.map((res) => (
                  <option key={res.id} value={res.id}>
                    {res.width} x {res.height}
                  </option>
                ))}
              </select>
            </div>

            {/* Partial Mode */}
            <div className="flex items-center justify-between py-3">
              <span className="text-sm font-medium text-gray-700">Partial Mode</span>
              <button
                onClick={() => toggleSwitch('partialMode')}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.partialMode ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.partialMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );

  // Render based on screen size
  if (isMobileOrTablet) {
    return (
      <BottomSheet
        isOpen={true}
        onClose={onClose}
        title="Settings"
        height={screenSize === 'mobile' ? '85vh' : '70vh'}
      >
        {renderContent()}
      </BottomSheet>
    );
  }

  // Desktop modal
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-hidden">
        {renderContent()}
      </div>
    </div>
  );
};

export default LarkXRSettingsModal;
