// src/components/LarkXR/LarkXRControlOptions.jsx
import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import LarkXRSettingsModal from './LarkXRSettingsModal';
import LarkXRHelpModal from './LarkXRHelpModal';
import LarkXRNetworkModal from './LarkXRNetworkModal';

const LarkXRControlOptions = ({
  larksr,
  isConnected,
  onRestartApp,
  showInDropdown = false,
  onClose,
  onOpenModal // Function to handle modal opening at parent level
}) => {
  // Use local modal state as fallback when onOpenModal is not provided
  const [activeModal, setActiveModal] = useState(null);

  const handleRestartApp = () => {
    if (window.confirm('Please confirm whether to restart the application')) {
      console.log('Restarting LarkXR application...');
      if (larksr && larksr.restartCloudApp) {
        larksr.restartCloudApp();
      }
      if (onRestartApp) {
        onRestartApp();
      }
    }
    if (onClose) onClose();
  };

  const handleNetworkInfo = () => {
    console.log('Opening Network modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('network', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('network');
    }
  };

  const handleSettings = () => {
    console.log('Opening Settings modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('settings', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('settings');
    }
  };

  const handleHelp = () => {
    console.log('Opening Help modal');
    if (onOpenModal) {
      // Use parent modal management
      onOpenModal('help', { larksr, isConnected });
      if (onClose) onClose();
    } else {
      // Use local modal management as fallback
      setActiveModal('help');
    }
  };

  const closeModal = () => {
    console.log('Closing modal');
    setActiveModal(null);
  };

  const controlOptions = [
    {
      id: 'restart',
      label: 'Restart App',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      ),
      onClick: handleRestartApp,
      description: 'Restart the cloud application'
    },
    {
      id: 'network',
      label: 'Network',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
        </svg>
      ),
      onClick: handleNetworkInfo,
      description: 'View network status and diagnostics'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      onClick: handleSettings,
      description: 'Configure LarkXR settings'
    },
    {
      id: 'help',
      label: 'Help',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      onClick: handleHelp,
      description: 'Get help and support information'
    }
  ];

  // Render control options and fallback modals
  return (
    <>
      {showInDropdown ? (
        // Dropdown items
        controlOptions.map((option) => (
          <button
            key={option.id}
            onClick={option.onClick}
            disabled={!isConnected && option.id === 'restart'}
            className={`flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md transition-colors ${
              !isConnected && option.id === 'restart' ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            title={option.description}
          >
            {option.icon}
            <span>{option.label}</span>
          </button>
        ))
      ) : (
        // Grid layout for bottom sheet
        <div className="grid grid-cols-2 gap-4">
          {controlOptions.map((option) => (
            <button
              key={option.id}
              onClick={option.onClick}
              disabled={!isConnected && option.id === 'restart'}
              className={`flex flex-col items-center gap-2 p-4 rounded-lg border transition-colors ${
                !isConnected && option.id === 'restart'
                  ? 'border-gray-200 bg-gray-50 opacity-50 cursor-not-allowed'
                  : 'border-gray-200 hover:bg-gray-50 hover:border-blue-300'
              }`}
            >
              <div className={`${!isConnected && option.id === 'restart' ? 'text-gray-400' : 'text-gray-600'}`}>
                {option.icon}
              </div>
              <span className={`text-sm font-medium ${!isConnected && option.id === 'restart' ? 'text-gray-400' : 'text-gray-900'}`}>
                {option.label}
              </span>
              <span className="text-xs text-gray-500 text-center">
                {option.description}
              </span>
            </button>
          ))}
        </div>
      )}

      {/* Fallback modals - only render when onOpenModal is not provided */}
      {!onOpenModal && activeModal === 'settings' && createPortal(
        <LarkXRSettingsModal
          larksr={larksr}
          isConnected={isConnected}
          onClose={closeModal}
        />,
        document.body
      )}
      {!onOpenModal && activeModal === 'help' && createPortal(
        <LarkXRHelpModal onClose={closeModal} />,
        document.body
      )}
      {!onOpenModal && activeModal === 'network' && createPortal(
        <LarkXRNetworkModal
          larksr={larksr}
          isConnected={isConnected}
          onClose={closeModal}
        />,
        document.body
      )}
    </>
  );
};

export default LarkXRControlOptions;
