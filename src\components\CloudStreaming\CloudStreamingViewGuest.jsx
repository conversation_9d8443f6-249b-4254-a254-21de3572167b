// src/components/CloudStreaming/CloudStreamingViewGuest.jsx
import React from "react";
import { LarkSR } from "larksr_websdk";
import PxyWebCommonUI from 'pxy_webcommonui';
import JoystickBottomImage from '../../assets/img/mobile/joy_stick_bottom.png';
import JoystickTopImage from '../../assets/img/mobile/joy_stick_top.png';

const { Joystick, Capabilities, Keyboard } = PxyWebCommonUI;

export default class CloudStreamingViewGuest extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      remoteReady: false,
      isLoading: true,
      connectionError: false,
      retryCount: 0,
      maxRetries: 3,
    }
    this.myRef = React.createRef();
    this.uiContainerRef = React.createRef();
    this.uiKeyboardRef = React.createRef();
    this.retryTimeout = null;
  }

  componentDidMount() {
    console.log("Guest: CloudStreamingViewGuest mounted with props:", this.props);
    // Only initialize if we have valid SDK ID, auth code and application ID
    if (this.props.authCode && this.props.applicationId) {
      // Add a small delay to ensure host is fully ready
      setTimeout(() => {
        this.initializeLarkSR();
      }, 500);
    } else {
      this.setState({
        isLoading: true,
        connectionError: false
      });
    }
  }

  componentDidUpdate(prevProps) {
    // Initialize if we just received valid props for the first time
    if (( !prevProps.authCode || !prevProps.applicationId) &&
        this.props.authCode && this.props.applicationId) {
      this.setState({
        isLoading: true,
        connectionError: false,
        retryCount: 0 // Reset retry count for new props
      });
      // Add delay for first-time initialization
      setTimeout(() => {
        this.initializeLarkSR();
      }, 500);
      return;
    }

    // Check if we need to reinitialize due to prop changes
    const authCodeChanged = prevProps.authCode !== this.props.authCode;
    const applicationIdChanged = prevProps.applicationId !== this.props.applicationId;

    // Reinitialize if auth code or application ID changes (and all are valid)
    if ((authCodeChanged || applicationIdChanged) &&
        this.props.authCode && this.props.applicationId &&
        (prevProps.authCode || prevProps.applicationId)) {

      console.log("Guest: Reinitializing LarkSR due to prop changes:", {
        authCodeChanged,
        applicationIdChanged,
        newAuthCode: this.props.authCode,
        newApplicationId: this.props.applicationId
      });

      // Reset retry count for prop changes
      this.setState({ retryCount: 0 });
      this.reinitializeLarkSR();
    }
  }

  // Separate method for reinitialization with proper cleanup sequencing
  async reinitializeLarkSR() {
    // Set loading state immediately to prevent multiple reinitializations
    this.setState({
      isLoading: true,
      connectionError: false
    });

    try {
      // Step 1: Cleanup existing instance
      await this.cleanup();

      // Step 2: Wait a bit to ensure complete cleanup
      await new Promise(resolve => setTimeout(resolve, 100));

      // Step 3: Clear any remaining DOM elements in the container
      if (this.myRef.current) {
        // Remove all child elements except our UI containers
        const children = Array.from(this.myRef.current.children);
        children.forEach(child => {
          if (child !== this.uiContainerRef.current &&
              child !== this.uiKeyboardRef.current &&
              !child.classList.contains('loading-overlay') &&
              !child.classList.contains('error-overlay')) {
            child.remove();
          }
        });
      }

      // Step 4: Initialize new instance
      this.initializeLarkSR();

    } catch (error) {
      console.error("Guest: Error during LarkSR reinitialization:", error);
      this.setState({
        connectionError: `Guest reinitialization failed: ${error.message}`,
        isLoading: false
      });
    }
  }

  initializeLarkSR() {
    const larksr = new LarkSR({
      rootElement: this.myRef.current,
      serverAddress: "https://ps.propvr.io:8181/",
      useSeparateMediaSharePeer: true,
    });

    const sdkId = "2b8edf14c2e348ab8211a742af4d5a15";
    const authCode = this.props.authCode;
    const applicationId = this.props.applicationId;
    const guestName = this.props.guestName || "guest";
    if (!sdkId || !authCode || !applicationId) {
      console.error("Guest: Missing required props: sdkId, authCode or applicationId");
      this.setState({ connectionError: true, isLoading: false });
      return;
    }

    // Validate auth code format (should be at least 8 characters)
    if (authCode.length < 8) {
      const error = `Guest: Invalid auth code format. Expected at least 8 characters, got ${authCode.length}`;
      console.error(error);
      this.setState({
        connectionError: error,
        isLoading: false
      });
      return;
    }

    console.log("Guest: Attempting LarkSR connection with sdkId:", sdkId, "and authCode:", authCode);

    // Initialize SDK with SDK ID (same as host)
    larksr.initSDKAuthCode(sdkId)
      .then(() => {
        console.log("Guest: SDK initialized successfully with sdkId:", sdkId);
        // Connect to the same session as host using auth code
        return larksr.connect({
          appliId: applicationId,
          playerMode: 1, // Guest mode (0 = host, 1 = guest)
          userType: 0,   // Guest user type
          nickname: guestName,
          authCode: authCode // Use auth code for connection
        });
      })
      .then(() => {
        console.log("Guest: LarkSR connected successfully");
        this.setState({
          isLoading: false,
          connectionError: false
        });

        // Notify parent component of successful connection if callback provided
        if (this.props.onConnectionSuccess) {
          this.props.onConnectionSuccess();
        }
      })
      .catch((e) => {
        console.error("Guest: LarkSR connection error:", e);
        console.error("Guest: Error details:", {
          message: e.message,
          code: e.code,
          sdkId: sdkId,
          authCode: authCode,
          applicationId: applicationId,
          retryCount: this.state.retryCount
        });

        // Implement automatic retry logic
        if (this.state.retryCount < this.state.maxRetries) {
          const nextRetryCount = this.state.retryCount + 1;
          const retryDelay = 1000 * nextRetryCount; // Exponential backoff: 1s, 2s, 3s

          console.log(`Guest: Connection failed, retrying in ${retryDelay}ms (attempt ${nextRetryCount}/${this.state.maxRetries})`);

          this.setState({
            retryCount: nextRetryCount,
            isLoading: true,
            connectionError: false
          });

          // Clear any existing retry timeout
          if (this.retryTimeout) {
            clearTimeout(this.retryTimeout);
          }

          // Schedule retry
          this.retryTimeout = setTimeout(() => {
            console.log(`Guest: Retrying connection (attempt ${nextRetryCount}/${this.state.maxRetries})`);
            this.cleanup();
            setTimeout(() => {
              this.initializeLarkSR();
            }, 100);
          }, retryDelay);
        } else {
          // Max retries reached, show error
          const errorMessage = e.message || JSON.stringify(e);
          this.setState({
            connectionError: `Guest connection failed after ${this.state.maxRetries} attempts: ${errorMessage}`,
            isLoading: false
          });

          // Notify parent component of connection error if callback provided
          if (this.props.onConnectionError) {
            this.props.onConnectionError(e);
          }
        }
      });

    // Set up event listeners
    this.setupEventListeners(larksr);
    this.larksr = larksr;

    // Initialize mobile controls if needed
    if (Capabilities.isMobile) {
      this.initializeMobileControls();
    }
  }

  setupEventListeners(larksr) {
    const events = [
      'connect', 'gotremotesteam', 'meidaloaded',
      'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
      'error', 'info', 'apprequestinput', 'resourcenotenough'
    ];

    events.forEach(event => {
      larksr.on(event, this.handleLarkEvent(event));
    });
  }

  initializeMobileControls() {
    if (this.uiContainerRef.current) {
      this.joystick = new Joystick({
        container: this.uiContainerRef.current,
        bottomImage: JoystickBottomImage,
        topImage: JoystickTopImage,
        onMove: (data) => {
          if (this.larksr) {
            this.larksr.sendGamepadInput({
              type: 'joystick',
              data: data
            });
          }
        }
      });
    }

    if (this.uiKeyboardRef.current) {
      this.keyboard = new Keyboard({
        container: this.uiKeyboardRef.current,
        onKeyPress: (key) => {
          if (this.larksr) {
            this.larksr.sendKeyboardInput(key);
          }
        }
      });
    }
  }

  handleLarkEvent = (eventName) => (e) => {
    console.log(`Guest LarkSR ${eventName} Event:`, e);

    switch(eventName) {
      case 'connect':
        console.log("Guest: LarkSR connected event received");
        this.setState({
          isLoading: false,
          connectionError: false
        });
        if (this.props.onConnectionSuccess) {
          this.props.onConnectionSuccess();
        }
        break;
      case 'meidaloaded':
        console.log("Guest: Media loaded successfully");
        this.setState({ remoteReady: true });
        break;
      case 'mediaplaysuccess':
        console.log("Guest: Media play success");
        this.joystick?.show();
        this.setState({ isLoading: false });
        break;
      case 'error':
        console.error("Guest: LarkSR error event:", e);
        const errorMessage = e.message || JSON.stringify(e);
        this.setState({
          connectionError: `Guest error: ${errorMessage}`,
          isLoading: false
        });
        if (this.props.onConnectionError) {
          this.props.onConnectionError(e);
        }
        break;
      case 'resourcenotenough':
        console.error("Guest: Resource not enough");
        this.setState({
          connectionError: "Resource not enough - please try again later",
          isLoading: false
        });
        break;
    }
  }

  async cleanup() {
    console.log("Guest: Starting LarkSR cleanup...");

    try {
      // Clear any pending retry timeout
      if (this.retryTimeout) {
        clearTimeout(this.retryTimeout);
        this.retryTimeout = null;
      }

      // Cleanup mobile controls first
      if (this.joystick) {
        this.joystick.destroy();
        this.joystick = null;
      }
      if (this.keyboard) {
        this.keyboard.destroy();
        this.keyboard = null;
      }

      // Cleanup LarkSR instance
      if (this.larksr) {
        // Remove event listeners before destroying
        const events = [
          'connect', 'gotremotesteam', 'meidaloaded',
          'mediaplaysuccess', 'mediaplayfailed', 'meidaplaymute',
          'error', 'info', 'apprequestinput', 'resourcenotenough'
        ];

        events.forEach(event => {
          try {
            this.larksr.off(event);
          } catch (e) {
            console.warn(`Guest: Failed to remove ${event} listener:`, e);
          }
        });

        // Destroy the SDK instance
        try {
          this.larksr.destroy();
        } catch (e) {
          console.warn("Guest: Error destroying LarkSR instance:", e);
        }

        this.larksr = null;
      }

      // Clear any video/canvas elements that might be left behind
      if (this.myRef.current) {
        const videos = this.myRef.current.querySelectorAll('video');
        const canvases = this.myRef.current.querySelectorAll('canvas');

        videos.forEach(video => {
          try {
            video.pause();
            video.srcObject = null;
            video.src = '';
            video.load();
          } catch (e) {
            console.warn("Guest: Error cleaning up video element:", e);
          }
        });

        canvases.forEach(canvas => {
          try {
            const ctx = canvas.getContext('2d');
            if (ctx) {
              ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
          } catch (e) {
            console.warn("Guest: Error cleaning up canvas element:", e);
          }
        });
      }

      console.log("Guest: LarkSR cleanup completed successfully");

    } catch (error) {
      console.error("Guest: Error during LarkSR cleanup:", error);
      throw error;
    }
  }

  render() {
    return (
      <div ref={this.myRef} style={{ position: 'relative', width: '100%', height: '100%' }}>
        <div ref={this.uiContainerRef} 
          style={{
            position: 'absolute',
            zIndex: 2000
          }}
        />
        <div ref={this.uiKeyboardRef} 
          style={{
            position: 'absolute',
            zIndex: 2000,
            bottom: 0,
            width: '100%'
          }}
        />
        
        {/* Loading overlay */}
        {this.state.isLoading && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.7)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-blue-500 mb-4"></div>
            <p className="text-white text-xl font-semibold">
              {this.state.retryCount > 0 ? `Retrying Connection...` : `Connecting to Host...`}
            </p>
            <p className="text-gray-300 mt-2">
              {this.state.retryCount > 0
                ? `Attempt ${this.state.retryCount + 1} of ${this.state.maxRetries + 1}`
                : `Please wait while we establish the connection`
              }
            </p>
          </div>
        )}

        {/* Error overlay */}
        {this.state.connectionError && !this.state.isLoading && (
          <div style={{
            position: "absolute",
            width: "100%",
            height: "100%",
            top: 0,
            left: 0,
            backgroundColor: "rgba(0, 0, 0, 0.8)",
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 3000
          }}>
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <p className="text-white text-xl font-semibold">Connection Error</p>
            <p className="text-gray-300 mt-2">Unable to connect to the streaming session</p>
            <button
              onClick={() => {
                this.setState({
                  connectionError: false,
                  isLoading: true,
                  retryCount: 0 // Reset retry count for manual retry
                });
                this.initializeLarkSR();
              }}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry Connection
            </button>
          </div>
        )}
      </div>
    );
  }

  componentWillUnmount() {
    // Clear any pending retry timeout
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    this.cleanup();
  }
}
