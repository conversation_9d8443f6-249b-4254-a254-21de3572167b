// src/components/LarkXR/LarkXRHelpModal.jsx
import React, { useState } from 'react';

const LarkXRHelpModal = ({ onClose }) => {
  const [activeTab, setActiveTab] = useState('controls');

  const tabs = [
    { id: 'controls', label: 'Controls', icon: '🎮' },
    { id: 'troubleshooting', label: 'Troubleshooting', icon: '🔧' },
    { id: 'shortcuts', label: 'Shortcuts', icon: '⌨️' },
    { id: 'about', label: 'About', icon: 'ℹ️' }
  ];

  const controlsContent = [
    {
      category: 'Basic Controls',
      items: [
        { action: 'Mouse Movement', description: 'Move cursor in the remote application' },
        { action: 'Left Click', description: 'Primary action/selection' },
        { action: 'Right Click', description: 'Context menu/secondary action' },
        { action: 'Scroll Wheel', description: 'Scroll up/down in applications' },
        { action: 'Keyboard Input', description: 'Type text and use keyboard shortcuts' }
      ]
    },
    {
      category: 'Touch Controls (Mobile)',
      items: [
        { action: 'Single Tap', description: 'Left click' },
        { action: 'Long Press', description: 'Right click' },
        { action: 'Two-finger Scroll', description: 'Scroll content' },
        { action: 'Pinch to Zoom', description: 'Zoom in/out (if supported)' },
        { action: 'Virtual Joystick', description: 'Game controller input' }
      ]
    },
    {
      category: 'Quality Controls',
      items: [
        { action: 'Auto Quality', description: 'Automatically adjusts based on connection' },
        { action: 'Manual Quality', description: 'Set specific resolution and bitrate' },
        { action: 'Performance Stats', description: 'View latency, FPS, and connection info' }
      ]
    }
  ];

  const troubleshootingContent = [
    {
      issue: 'High Latency',
      solutions: [
        'Check your internet connection speed',
        'Close other bandwidth-intensive applications',
        'Switch to a wired connection if using WiFi',
        'Lower the video quality setting',
        'Move closer to your WiFi router'
      ]
    },
    {
      issue: 'Poor Video Quality',
      solutions: [
        'Increase video quality in settings',
        'Check available bandwidth',
        'Ensure stable internet connection',
        'Close other streaming applications',
        'Try switching to "Quality First" optimization'
      ]
    },
    {
      issue: 'Audio Issues',
      solutions: [
        'Check audio quality settings',
        'Verify system audio is not muted',
        'Try refreshing the connection',
        'Check browser audio permissions',
        'Test with different audio output device'
      ]
    },
    {
      issue: 'Connection Drops',
      solutions: [
        'Enable auto-reconnect in settings',
        'Check network stability',
        'Restart your router/modem',
        'Try a different network',
        'Contact your internet service provider'
      ]
    }
  ];

  const shortcutsContent = [
    {
      category: 'General',
      shortcuts: [
        { keys: 'F11', action: 'Toggle fullscreen' },
        { keys: 'Ctrl + Shift + S', action: 'Show/hide statistics' },
        { keys: 'Ctrl + Shift + Q', action: 'Quick quality toggle' },
        { keys: 'Ctrl + Shift + R', action: 'Restart application' }
      ]
    },
    {
      category: 'Controls',
      shortcuts: [
        { keys: 'Ctrl + Shift + H', action: 'Show/hide controls' },
        { keys: 'Ctrl + Shift + M', action: 'Toggle microphone' },
        { keys: 'Ctrl + Shift + C', action: 'Open chat' },
        { keys: 'Ctrl + Shift + P', action: 'Open participants' }
      ]
    }
  ];

  const aboutContent = {
    version: '1.0.0',
    buildDate: new Date().toLocaleDateString(),
    features: [
      'High-quality video streaming',
      'Low-latency input handling',
      'Adaptive quality control',
      'Multi-platform support',
      'Real-time performance monitoring'
    ]
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'controls':
        return (
          <div className="space-y-6">
            {controlsContent.map((section, index) => (
              <div key={index}>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.category}</h3>
                <div className="space-y-2">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex justify-between items-start">
                      <span className="font-medium text-gray-700 w-1/3">{item.action}</span>
                      <span className="text-gray-600 w-2/3">{item.description}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        );

      case 'troubleshooting':
        return (
          <div className="space-y-6">
            {troubleshootingContent.map((item, index) => (
              <div key={index}>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{item.issue}</h3>
                <ul className="space-y-2">
                  {item.solutions.map((solution, solutionIndex) => (
                    <li key={solutionIndex} className="flex items-start">
                      <span className="text-blue-500 mr-2">•</span>
                      <span className="text-gray-600">{solution}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        );

      case 'shortcuts':
        return (
          <div className="space-y-6">
            {shortcutsContent.map((section, index) => (
              <div key={index}>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{section.category}</h3>
                <div className="space-y-2">
                  {section.shortcuts.map((shortcut, shortcutIndex) => (
                    <div key={shortcutIndex} className="flex justify-between items-center">
                      <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                        {shortcut.keys}
                      </span>
                      <span className="text-gray-600 flex-1 ml-4">{shortcut.action}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        );

      case 'about':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">LarkXR Control Panel</h3>
              <p className="text-gray-600">Version {aboutContent.version}</p>
              <p className="text-sm text-gray-500">Built on {aboutContent.buildDate}</p>
            </div>

            <div>
              <h4 className="text-lg font-semibold text-gray-900 mb-3">Features</h4>
              <ul className="space-y-2">
                {aboutContent.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="text-green-500 mr-2">✓</span>
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-lg font-semibold text-blue-900 mb-2">Support</h4>
              <p className="text-blue-800 text-sm">
                For technical support or questions, please contact your system administrator 
                or visit our documentation portal.
              </p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999999]">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Help & Support</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="flex">
          {/* Sidebar */}
          <div className="w-1/4 bg-gray-50 p-4 border-r border-gray-200">
            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full text-left px-3 py-2 rounded-md transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-100 text-blue-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LarkXRHelpModal;
