// src/components/LarkXR/LarkXRNetworkModal.jsx
import React, { useState, useEffect } from 'react';

const LarkXRNetworkModal = ({ larksr, isConnected, onClose }) => {
  const [networkStats, setNetworkStats] = useState({
    latency: 0,
    bandwidth: 0,
    packetLoss: 0,
    jitter: 0,
    connectionType: 'unknown',
    serverRegion: 'unknown',
    protocol: 'WebRTC',
    encryption: 'DTLS-SRTP',
    downloadSpeed: 0,
    uploadSpeed: 0,
    serverLatency: 0,
    videoLatency: 0,
    audioLatency: 0,
    frameDrops: 0,
    reconnections: 0,
    dataUsage: 0,
    sessionDuration: 0
  });

  const [connectionHistory, setConnectionHistory] = useState([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (larksr && isConnected) {
      fetchNetworkStats();
      const interval = setInterval(fetchNetworkStats, 2000); // Update every 2 seconds
      return () => clearInterval(interval);
    }
  }, [larksr, isConnected]);

  const fetchNetworkStats = async () => {
    try {
      if (larksr && larksr.getConnectionStats) {
        const stats = await larksr.getConnectionStats();
        setNetworkStats(prev => ({
          ...prev,
          ...stats,
          timestamp: Date.now()
        }));

        // Add to history (keep last 10 entries)
        setConnectionHistory(prev => {
          const newHistory = [...prev, { ...stats, timestamp: Date.now() }];
          return newHistory.slice(-10);
        });
      } else {
        // Mock data for demonstration
        const mockStats = {
          latency: Math.floor(Math.random() * 20) + 35,
          bandwidth: Math.floor(Math.random() * 5000) + 15000,
          packetLoss: Math.random() * 0.5,
          jitter: Math.floor(Math.random() * 5) + 2,
          connectionType: 'wifi',
          serverRegion: 'US-East',
          protocol: 'WebRTC',
          encryption: 'DTLS-SRTP',
          downloadSpeed: Math.floor(Math.random() * 10000) + 20000,
          uploadSpeed: Math.floor(Math.random() * 2000) + 5000,
          serverLatency: Math.floor(Math.random() * 15) + 25,
          videoLatency: Math.floor(Math.random() * 10) + 40,
          audioLatency: Math.floor(Math.random() * 5) + 15,
          frameDrops: Math.floor(Math.random() * 3),
          reconnections: Math.floor(Math.random() * 2),
          dataUsage: Math.floor(Math.random() * 500) + 1200, // MB
          sessionDuration: Math.floor(Date.now() / 1000) - Math.floor(Math.random() * 3600) // seconds
        };
        setNetworkStats(prev => ({ ...prev, ...mockStats }));
      }
    } catch (error) {
      console.warn('Failed to fetch network stats:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchNetworkStats();
    setTimeout(() => setIsRefreshing(false), 500);
  };

  const getConnectionQuality = () => {
    const { latency, packetLoss, bandwidth } = networkStats;
    
    if (latency < 50 && packetLoss < 1 && bandwidth > 10000) {
      return { status: 'excellent', color: 'text-green-600', bgColor: 'bg-green-100' };
    } else if (latency < 100 && packetLoss < 3 && bandwidth > 5000) {
      return { status: 'good', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    } else {
      return { status: 'poor', color: 'text-red-600', bgColor: 'bg-red-100' };
    }
  };

  const quality = getConnectionQuality();

  const formatBandwidth = (bw) => {
    if (bw > 1000) {
      return `${(bw / 1000).toFixed(1)} Mbps`;
    }
    return `${bw} Kbps`;
  };

  const getNetworkIcon = () => {
    switch (networkStats.connectionType) {
      case 'wifi':
        return '📶';
      case 'ethernet':
        return '🔌';
      case 'cellular':
        return '📱';
      default:
        return '🌐';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[999999]">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <h2 className="text-xl font-semibold text-gray-900">Network Status</h2>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${quality.color} ${quality.bgColor}`}>
              {quality.status.toUpperCase()}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className={`p-2 rounded-md transition-colors ${
                isRefreshing 
                  ? 'text-gray-400 cursor-not-allowed' 
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="Refresh"
            >
              <svg 
                className={`w-5 h-5 ${isRefreshing ? 'animate-spin' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Connection Overview */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-gray-900">{networkStats.latency}ms</div>
              <div className="text-sm text-gray-600">Total Latency</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-gray-900">{formatBandwidth(networkStats.bandwidth)}</div>
              <div className="text-sm text-gray-600">Bandwidth</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-gray-900">{networkStats.packetLoss.toFixed(2)}%</div>
              <div className="text-sm text-gray-600">Packet Loss</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg text-center">
              <div className="text-2xl font-bold text-gray-900">{networkStats.jitter}ms</div>
              <div className="text-sm text-gray-600">Jitter</div>
            </div>
          </div>

          {/* Detailed Metrics */}
          <div className="grid grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Network Performance</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Download Speed</span>
                  <span className="font-medium">{formatBandwidth(networkStats.downloadSpeed)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Upload Speed</span>
                  <span className="font-medium">{formatBandwidth(networkStats.uploadSpeed)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Server Latency</span>
                  <span className="font-medium">{networkStats.serverLatency}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Frame Drops</span>
                  <span className="font-medium">{networkStats.frameDrops}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Reconnections</span>
                  <span className="font-medium">{networkStats.reconnections}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Stream Latency</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Video Latency</span>
                  <span className="font-medium">{networkStats.videoLatency}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Audio Latency</span>
                  <span className="font-medium">{networkStats.audioLatency}ms</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Data Usage</span>
                  <span className="font-medium">{(networkStats.dataUsage / 1024).toFixed(1)} GB</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Session Duration</span>
                  <span className="font-medium">
                    {Math.floor(networkStats.sessionDuration / 60)}m {networkStats.sessionDuration % 60}s
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Connection Details */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Connection Details</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Connection Type</span>
                <span className="font-medium flex items-center gap-2">
                  <span>{getNetworkIcon()}</span>
                  {networkStats.connectionType}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Server Region</span>
                <span className="font-medium">{networkStats.serverRegion}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Protocol</span>
                <span className="font-medium">{networkStats.protocol}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Encryption</span>
                <span className="font-medium flex items-center gap-2">
                  🔒 {networkStats.encryption}
                </span>
              </div>
            </div>
          </div>

          {/* Connection History Chart */}
          {connectionHistory.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Latency History</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-end justify-between h-32 gap-1">
                  {connectionHistory.map((stat, index) => {
                    const height = Math.max((stat.latency / 200) * 100, 5); // Scale to 200ms max
                    const color = stat.latency < 50 ? 'bg-green-500' : stat.latency < 100 ? 'bg-yellow-500' : 'bg-red-500';
                    return (
                      <div
                        key={index}
                        className={`${color} rounded-t transition-all duration-300`}
                        style={{ height: `${height}%`, width: `${100 / connectionHistory.length}%` }}
                        title={`${stat.latency}ms`}
                      />
                    );
                  })}
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-2">
                  <span>-20s</span>
                  <span>Now</span>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recommendations</h3>
            <div className="space-y-2">
              {networkStats.latency > 100 && (
                <div className="flex items-start gap-2 p-3 bg-yellow-50 rounded-lg">
                  <span className="text-yellow-600">⚠️</span>
                  <div>
                    <div className="font-medium text-yellow-800">High Latency Detected</div>
                    <div className="text-sm text-yellow-700">Consider switching to a wired connection or moving closer to your router.</div>
                  </div>
                </div>
              )}
              
              {networkStats.packetLoss > 2 && (
                <div className="flex items-start gap-2 p-3 bg-red-50 rounded-lg">
                  <span className="text-red-600">🚨</span>
                  <div>
                    <div className="font-medium text-red-800">Packet Loss Detected</div>
                    <div className="text-sm text-red-700">Network instability may affect streaming quality. Check your internet connection.</div>
                  </div>
                </div>
              )}
              
              {networkStats.bandwidth < 5000 && (
                <div className="flex items-start gap-2 p-3 bg-orange-50 rounded-lg">
                  <span className="text-orange-600">📶</span>
                  <div>
                    <div className="font-medium text-orange-800">Low Bandwidth</div>
                    <div className="text-sm text-orange-700">Consider lowering video quality for better performance.</div>
                  </div>
                </div>
              )}
              
              {quality.status === 'excellent' && (
                <div className="flex items-start gap-2 p-3 bg-green-50 rounded-lg">
                  <span className="text-green-600">✅</span>
                  <div>
                    <div className="font-medium text-green-800">Excellent Connection</div>
                    <div className="text-sm text-green-700">Your connection is optimal for high-quality streaming.</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default LarkXRNetworkModal;
