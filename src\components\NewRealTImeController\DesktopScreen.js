import React, { useState, useEffect, useRef } from 'react';
import { Tooltip } from "flowbite-react";
import { MicIcon, MicOffIcon, VideoIcon, VideoOffIcon, ChatIcon, EndCallIcon, EyeIcon, EyeOffIcon, StopScreenShareIcon, FullScreenIcon, UsersIcon, ScreenShareIcon, ActiveChat, ActiveMember, ActiveDropdown } from '../../assets/SvgIcons';
import Timer from "../../Tools/ToolComponents/Timer";
import GuestTimer from "../../Tools/ToolComponents/GuestTimer";
import LarkXRControlOptions from '../LarkXR/LarkXRControlOptions';
import LarkXRSettingsModal from '../LarkXR/LarkXRSettingsModal';
import LarkXRHelpModal from '../LarkXR/LarkXRHelpModal';
import LarkXRNetworkModal from '../LarkXR/LarkXRNetworkModal';

const DesktopControls = ({
    Audio,
    Video,
    ChatOpen,
    MembersOpen,
    UnreadCount,
    userscount,
    handleAudioControl,
    handleVideoControl,
    handleTabControl,
    handleEndSession,
    handleStopSharing,
    toggleFullscreen,
    handleInvertControls,
    ShowControls,
    handleShareProject,
    switchingProject,
    SessionDetails,
    roomId,
    isGuest = false
}) => {

    const [showMenu, setShowMenu] = useState(false);
    const [activeModal, setActiveModal] = useState(null);
    const [modalProps, setModalProps] = useState({});
    const menuRef = useRef(null);

    const toggleMenu = () => {
        setShowMenu(!showMenu);
    };

    const handleOpenModal = (modalType, props = {}) => {
        console.log('Desktop: Opening modal:', modalType);
        setActiveModal(modalType);
        setModalProps(props);
    };

    const handleCloseModal = () => {
        console.log('Desktop: Closing modal');
        setActiveModal(null);
        setModalProps({});
    };

    // Close menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (menuRef.current && !menuRef.current.contains(event.target)) {
                setShowMenu(false);
            }
        };

        // Add event listener when menu is open
        if (showMenu) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        // Clean up event listener
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showMenu]);

    // Render Share Project button or Project Name as dropdown
    const ShareProjectButton = () => {
        // Check if a project is being shared - more robust checking
        const isProjectShared = SessionDetails?.project_id &&
                               SessionDetails.project_id !== 'undefined' &&
                               SessionDetails.project_id !== null &&
                               SessionDetails.type !== 'default';

        // If project is being shared, show project name and stop sharing button
        if (isProjectShared) {
            return (
                <div className="flex items-center gap-2">
                    <button
                        onClick={handleShareProject}
                        className="flex items-center bg-[#F3F4F6] hover:bg-[#E5E7EB] rounded-lg px-3 py-1 h-8"
                    >
                        <span className="text-gray-700 font-medium truncate max-w-[150px]">
                            {SessionDetails?.projectName || "Project"}
                        </span>
                        <svg width="10" height="6" className="ml-2" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 4.99997C9 4.99997 6.05407 1 5 1C3.94587 1 1 5 1 5" stroke="#141B34" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                    </button>
                </div>
            );
        }

        // Otherwise show Share Project button
        return (
            <button
                className={`flex bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded-lg items-center justify-center w-40 ${switchingProject ? 'opacity-75' : ''}`}
                onClick={handleShareProject}
                disabled={switchingProject}
            >
                {switchingProject ? (
                    <>
                        <span className="mr-2 animate-spin inline-block h-4 w-4 rounded-full border-1 border-white border-t-transparent"></span>
                        Switching...
                    </>
                ) : (
                    <>
                        <svg width="16" height="16" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                            <path d="M9.35883 0.603879C9.20615 0.670162 9.07018 0.763677 8.95586 0.881838L4.04027 5.96277C3.55965 6.45955 3.57268 7.2495 4.06943 7.73016C4.56618 8.21082 5.35607 8.19779 5.83669 7.701L8.65526 4.78763L8.83233 15.5195C8.84374 16.2107 9.41288 16.7602 10.1028 16.7488C10.7927 16.7374 11.3434 16.1695 11.332 15.4783L11.1549 4.74639L14.0681 7.56519C14.5648 8.04585 15.3547 8.03281 15.8353 7.53603C16.075 7.28827 16.1922 6.96627 16.1869 6.64629C16.1817 6.3263 16.0539 6.00834 15.8062 5.76863L10.7256 0.852637C10.6087 0.738292 10.4685 0.649334 10.3137 0.588124C10.0067 0.466914 9.66171 0.472606 9.35883 0.603879Z" fill="white" />
                            <path d="M17.5399 12.8753L17.6018 16.6251C17.6131 17.315 17.0612 17.8843 16.3726 17.8956L3.87426 18.1019C3.1856 18.1132 2.61519 17.5625 2.6038 16.8725L2.54193 13.1228C2.53053 12.4316 1.96139 11.8821 1.27148 11.8935C0.581575 11.9048 0.0308697 12.4728 0.0422745 13.164L0.104145 16.9138C0.138256 18.9812 1.84829 20.6358 3.91551 20.6017L16.4138 20.3955C18.481 20.3614 20.1355 18.6512 20.1014 16.5838L20.0396 12.8341C20.0281 12.1428 19.459 11.5934 18.7691 11.6048C18.0792 11.6161 17.5285 12.1841 17.5399 12.8753Z" fill="white" />
                        </svg>
                        Share Project
                    </>
                )}
            </button>
        );
    };

    // Control Button Component
    const ControlButton = ({ tooltip, onClick, icon, badge, endCall = false, isActive = false }) => {
        return (
            <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content={tooltip}
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
            >
                <button
                    onClick={onClick}
                    className={`w-8 h-8 rounded-lg relative cursor-pointer transition-all duration-200 flex items-center justify-center ${endCall ? 'bg-[#FDF2F2] hover:bg-[#ff6666] text-white border-[none]' :
                        isActive ? 'bg-white border-1 border-[#1C64F2] hover:bg-gray-50' :
                            'bg-[#F3F4F6] hover:bg-[#E5E7EB] border-[none]'
                        }`}
                >
                    {badge && badge > 0 && (
                        <span className="absolute font-bold text-[10px] text-white h-4 w-4 flex items-center justify-center rounded-full right-0 top-0 bg-[#ff4444]">
                            {badge}
                        </span>
                    )}
                    <div className="flex items-center justify-center w-5 h-5">
                        {icon}
                    </div>
                </button>
            </Tooltip>
        );
    };

    return (
        <div className="hidden md:block" >
            <div className="md:flex md:h-12 md:items-center justify-between px-2">
                {/* Left side - Timer */}
                <div className="flex items-center">

                    {/* Timer component */}
                    {isGuest ? (
                        <GuestTimer
                            position={"top"}
                            Config={SessionDetails}
                            start={SessionDetails?.start_time || new Date()}
                            roomId={roomId}
                            trackInactivity={false}
                            trackMaxtimeout={false}
                        />
                    ) : (
                        <Timer
                            position={"top"}
                            start={SessionDetails?.start || new Date()}
                            roomId={roomId}
                            trackInactivity={false}
                            trackMaxtimeout={true}
                            SessionDetails={SessionDetails}
                        />
                    )}
                </div>

                {/* Right side - Share Project button and controls */}
                <div className="flex items-center gap-3">
                    {!isGuest && <ShareProjectButton />}

                    <ControlButton
                        tooltip={Video ? "Turn Off Camera" : "Turn On Camera"}
                        onClick={handleVideoControl}
                        icon={Video ? <VideoIcon /> : <VideoOffIcon />}
                    />

                    <ControlButton
                        tooltip={Audio ? "Mute" : "Unmute"}
                        onClick={handleAudioControl}
                        icon={Audio ? <MicIcon /> : <MicOffIcon />}
                    />

                    <ControlButton
                        tooltip="Chat"
                        onClick={() => handleTabControl("CHAT")}
                        icon={ChatOpen ? <ActiveChat /> : <ChatIcon />}
                        badge={UnreadCount > 0 ? UnreadCount : null}
                        isActive={ChatOpen}
                    />

                    <ControlButton
                        tooltip="Members"
                        onClick={() => handleTabControl("MEMBERS")}
                        icon={MembersOpen ? <ActiveMember /> : <UsersIcon />}
                        badge={userscount > 0 ? userscount : null}
                        isActive={MembersOpen}
                    />

                    {/* Stop Sharing button - only shown when a project is being shared AND not a guest */}
                    {!isGuest && SessionDetails?.project_id &&
                     SessionDetails.project_id !== 'undefined' &&
                     SessionDetails.project_id !== null &&
                     SessionDetails.type !== 'default' && (
                        <Tooltip
                            arrow={7.5}
                            background="rgb(0 0 0 / 0.1)"
                            border="#fff"
                            color="#fff"
                            content="Stop Sharing"
                            fadeDuration={0}
                            fadeEasing="linear"
                            fixed={false}
                            fontFamily="inherit"
                            fontSize="1rem"
                            offset={0}
                            padding={3}
                            direction="down"
                            radius={4}
                            zIndex={1}
                        >
                            <button
                                onClick={handleStopSharing}
                                className="w-8 h-8 rounded-lg bg-[#F3F4F6] hover:bg-[#E5E7EB] flex items-center justify-center"
                            >
                                <div className="flex items-center justify-center w-5 h-5">
                                    <StopScreenShareIcon />
                                </div>
                            </button>
                        </Tooltip>
                    )}

                    <Tooltip
                        arrow={7.5}
                        background="rgb(0 0 0 / 0.1)"
                        border="#fff"
                        color="#fff"
                        content="Fullscreen"
                        fadeDuration={0}
                        fadeEasing="linear"
                        fixed={false}
                        fontFamily="inherit"
                        fontSize="1rem"
                        offset={0}
                        padding={3}
                        direction="down"
                        radius={4}
                        zIndex={1}
                    >
                        <button
                            onClick={() => toggleFullscreen(true)}
                            className="w-8 h-8 rounded-lg bg-[#F3F4F6] hover:bg-[#E5E7EB] flex items-center justify-center"
                        >
                            <FullScreenIcon />
                        </button>
                    </Tooltip>

                    {/* Dropdown menu for both host and guest */}
                    <div className="relative" ref={menuRef}>
                        <button
                            onClick={toggleMenu}
                            className={`w-8 h-8 rounded-lg flex items-center justify-center ${showMenu ? 'bg-white border-1 border-[#1C64F2]' : 'bg-[#F3F4F6] hover:bg-[#E5E7EB] border-[none]'}`}
                        >
                            {showMenu ? <ActiveDropdown /> : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                                </svg>
                            )}
                        </button>

                        {showMenu && (
                            <div className="absolute top-10 right-0 bg-white rounded-lg shadow-lg p-2 w-48 z-50">
                                <button
                                    onClick={() => {
                                        handleInvertControls(ShowControls);
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md"
                                >
                                    <EyeIcon />
                                    <span>Hide Interface</span>
                                </button>

                                {/* LarkXR Controls - only show for LarkXR sessions */}
                                {SessionDetails?.type === 'lark' && (
                                    <>
                                        <div className="border-t border-gray-200 my-1"></div>
                                        <div className="px-2 py-1">
                                            <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">LarkXR Controls</span>
                                        </div>
                                        <LarkXRControlOptions
                                            larksr={null} // Will be passed from parent in future update
                                            isConnected={true} // Assume connected if in LarkXR session
                                            showInDropdown={true}
                                            onClose={() => setShowMenu(false)}
                                            onOpenModal={handleOpenModal}
                                        />
                                        <div className="border-t border-gray-200 my-1"></div>
                                    </>
                                )}

                                {/* Stop Sharing option removed from dropdown as it's already available as a button */}

                                <button
                                    onClick={() => {
                                        handleEndSession();
                                        setShowMenu(false);
                                    }}
                                    className="flex items-center gap-2 w-full text-left p-2 hover:bg-gray-100 rounded-md text-red-500"
                                >
                                    <EndCallIcon />
                                    <span>End Session</span>
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            {/* LarkXR Modals */}
            {activeModal === 'settings' && (
                <LarkXRSettingsModal
                    larksr={modalProps.larksr}
                    isConnected={modalProps.isConnected}
                    onClose={handleCloseModal}
                />
            )}

            {activeModal === 'network' && (
                <LarkXRNetworkModal
                    larksr={modalProps.larksr}
                    isConnected={modalProps.isConnected}
                    onClose={handleCloseModal}
                />
            )}

            {activeModal === 'help' && (
                <LarkXRHelpModal onClose={handleCloseModal} />
            )}
        </div>
    );
};

export default DesktopControls;